# MongoDB Configuration
MONGO_URI=mongodb://localhost:27017

# Azure Blob Storage Configuration
AZURE_BLOB_CONN=DefaultEndpointsProtocol=https;AccountName=your_account;AccountKey=your_key;EndpointSuffix=core.windows.net
AZURE_CONTAINER=documents

# Worker Configuration
# JOB_ID is set by KEDA or manually for testing
# Example: JOB_ID=68a6b73df6d117b0aeaa695e

# API Configuration
# Port is set in app.py (default: 5001)

# Worker Metrics Configuration
# Metrics endpoint runs on port 9100 by default
